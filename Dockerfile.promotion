# Dockerfile.promotion
# Dockerfile for Promotion microservice

FROM node:22.14-alpine AS builder

WORKDIR /app

# Copy package files
COPY package*.json ./
COPY tsconfig*.json ./
COPY nest-cli.json ./

# Install dependencies
RUN npm ci && npm cache clean --force

# Copy source code
COPY apps/promotion ./apps/promotion

# Build the application
RUN npm run build promotion

# Production stage
FROM node:22.14-alpine AS production

WORKDIR /app

# Copy package files
COPY package*.json ./

# Install only production dependencies
RUN npm ci --omit=dev && npm cache clean --force

# Copy built application
COPY --from=builder /app/dist ./dist
COPY --from=builder /app/node_modules ./node_modules

# Create non-root user
RUN addgroup -g 1001 -S nodejs
RUN adduser -S nestjs -u 1001

# Change ownership
RUN chown -R nestjs:nodejs /app
USER nestjs

# Expose port
EXPOSE 3001

# Health check (check if TCP port is listening)
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD netstat -an | grep 3001 || exit 1

# Start the application
CMD ["node", "dist/apps/promotion/main"]
