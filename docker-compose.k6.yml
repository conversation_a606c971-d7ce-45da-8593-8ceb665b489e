# docker-compose.k6.yml
# Docker Compose configuration for running k6 TCP transport tests

version: '3.8'

services:
  # BFF Service
  bff:
    build:
      context: .
      dockerfile: Dockerfile.bff
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - PROMOTION_SERVICE_HOST=promotion
      - PROMOTION_SERVICE_PORT=3001
    depends_on:
      - promotion
    networks:
      - microservices
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Promotion Microservice
  promotion:
    build:
      context: .
      dockerfile: Dockerfile.promotion
    ports:
      - "3001:3001"
    environment:
      - NODE_ENV=production
      - TCP_HOST=0.0.0.0
      - TCP_PORT=3001
    networks:
      - microservices
    healthcheck:
      test: ["CMD", "netstat", "-an", "|", "grep", "3001"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # k6 Load Testing Service
  k6:
    image: grafana/k6:latest
    volumes:
      - ./k6-scripts:/scripts
      - ./test-results:/results
    environment:
      - BASE_URL=http://bff:3000
      - PROMOTION_SERVICE_URL=http://promotion:3001
      - OUTPUT_DIR=/results
    networks:
      - microservices
    depends_on:
      bff:
        condition: service_healthy
      promotion:
        condition: service_healthy
    profiles:
      - testing

  # k6 with specific test scenarios
  k6-smoke:
    extends: k6
    command: run /scripts/tcp-transport-test.js --env SCENARIO=smoke
    profiles:
      - smoke-test

  k6-load:
    extends: k6
    command: run /scripts/tcp-transport-test.js --env SCENARIO=load
    profiles:
      - load-test

  k6-stress:
    extends: k6
    command: run /scripts/tcp-transport-test.js --env SCENARIO=stress
    profiles:
      - stress-test

  k6-connection-pool:
    extends: k6
    command: run /scripts/tcp-connection-pool-test.js
    profiles:
      - connection-test

  k6-latency:
    extends: k6
    command: run /scripts/tcp-latency-test.js
    profiles:
      - latency-test

  # Monitoring (optional)
  prometheus:
    image: prom/prometheus:latest
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
    networks:
      - microservices
    profiles:
      - monitoring

  grafana:
    image: grafana/grafana:latest
    ports:
      - "3001:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
    volumes:
      - grafana-storage:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources
    networks:
      - microservices
    profiles:
      - monitoring

networks:
  microservices:
    driver: bridge

volumes:
  grafana-storage:
