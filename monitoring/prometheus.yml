# monitoring/prometheus.yml
# Prometheus configuration for monitoring k6 tests

global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  # - "first_rules.yml"
  # - "second_rules.yml"

scrape_configs:
  # k6 metrics (if using Prometheus output)
  - job_name: 'k6'
    static_configs:
      - targets: ['localhost:6565']
    scrape_interval: 5s
    metrics_path: /metrics

  # BFF service metrics (if implemented)
  - job_name: 'bff-service'
    static_configs:
      - targets: ['bff:3000']
    scrape_interval: 15s
    metrics_path: /metrics

  # Promotion service metrics (if implemented)
  - job_name: 'promotion-service'
    static_configs:
      - targets: ['promotion:3001']
    scrape_interval: 15s
    metrics_path: /metrics

  # Node exporter for system metrics
  - job_name: 'node-exporter'
    static_configs:
      - targets: ['node-exporter:9100']
    scrape_interval: 15s
