{"name": "bff", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"build": "nest build", "format": "prettier --write \"apps/**/*.ts\" \"libs/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/apps/bff/main", "start:promotion": "nest start promotion", "start:promotion:dev": "nest start promotion --watch", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./apps/bff/test/jest-e2e.json", "k6:tcp-test": "k6 run k6-scripts/tcp-transport-test.js", "k6:tcp-load": "k6 run k6-scripts/tcp-transport-test.js --env SCENARIO=load", "k6:tcp-stress": "k6 run k6-scripts/tcp-transport-test.js --env SCENARIO=stress", "k6:tcp-spike": "k6 run k6-scripts/tcp-transport-test.js --env SCENARIO=spike", "k6:tcp-soak": "k6 run k6-scripts/tcp-transport-test.js --env SCENARIO=soak", "k6:connection-pool": "k6 run k6-scripts/tcp-connection-pool-test.js", "k6:latency": "k6 run k6-scripts/tcp-latency-test.js", "k6:all": "npm run k6:tcp-test && npm run k6:connection-pool && npm run k6:latency", "k6:smoke": "k6 run k6-scripts/tcp-transport-test.js --env SCENARIO=smoke"}, "dependencies": {"@nestjs/common": "^11.0.1", "@nestjs/core": "^11.0.1", "@nestjs/microservices": "^11.1.1", "@nestjs/platform-express": "^11.0.1", "reflect-metadata": "^0.2.2", "rxjs": "^7.8.1"}, "devDependencies": {"@eslint/eslintrc": "^3.2.0", "@eslint/js": "^9.18.0", "@nestjs/cli": "^11.0.0", "@nestjs/schematics": "^11.0.0", "@nestjs/testing": "^11.0.1", "@swc/cli": "^0.6.0", "@swc/core": "^1.10.7", "@types/express": "^5.0.0", "@types/jest": "^29.5.14", "@types/node": "^22.10.7", "@types/supertest": "^6.0.2", "eslint": "^9.18.0", "eslint-config-prettier": "^10.0.1", "eslint-plugin-prettier": "^5.2.2", "globals": "^16.0.0", "jest": "^29.7.0", "prettier": "^3.4.2", "source-map-support": "^0.5.21", "supertest": "^7.0.0", "ts-jest": "^29.2.5", "ts-loader": "^9.5.2", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.7.3", "typescript-eslint": "^8.20.0"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": ".", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "./coverage", "testEnvironment": "node", "roots": ["<rootDir>/apps/"]}}