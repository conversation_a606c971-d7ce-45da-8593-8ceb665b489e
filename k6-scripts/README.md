# k6 TCP Transport Performance Testing

This directory contains k6 scripts specifically designed to test the performance of TCP transport in NestJS microservices architecture.

## Overview

The test suite focuses on evaluating:
- TCP transport performance between BFF and microservices
- Connection pooling and reuse efficiency
- Latency analysis under various load conditions
- Error handling and resilience
- Throughput and response time metrics

## Architecture Under Test

```
┌─────────────┐    HTTP     ┌─────────────┐    TCP     ┌─────────────┐
│   k6 Test   │ ────────► │ BFF Service │ ────────► │ Promotion   │
│   Scripts   │           │ (Port 3000) │           │ Service     │
└─────────────┘           └─────────────┘           │ (Port 3001) │
                                                    └─────────────┘
```

## Test Scripts

### 1. `tcp-transport-test.js` - Main Performance Test
**Purpose**: Comprehensive TCP transport performance testing with multiple scenarios.

**Features**:
- Basic TCP communication testing
- Variable timeout testing
- Concurrent request simulation
- Error handling and resilience testing
- Multiple load scenarios (smoke, load, stress, spike)

**Usage**:
```bash
# Run smoke test (default)
npm run k6:tcp-test

# Run specific scenarios
npm run k6:tcp-load    # Load testing
npm run k6:tcp-stress  # Stress testing
npm run k6:tcp-spike   # Spike testing

# Custom scenario
k6 run tcp-transport-test.js --env SCENARIO=soak
```

### 2. `tcp-connection-pool-test.js` - Connection Pool Analysis
**Purpose**: Specialized testing for TCP connection pooling behavior.

**Features**:
- Connection reuse rate measurement
- Pool exhaustion simulation
- Connection lifecycle management
- Parallel connection usage analysis

**Usage**:
```bash
k6 run tcp-connection-pool-test.js
```

### 3. `tcp-latency-test.js` - Latency Analysis
**Purpose**: Detailed latency measurement and analysis.

**Features**:
- Minimal latency baseline measurement
- Controlled delay testing
- Concurrent request latency impact
- Load-dependent latency analysis

**Usage**:
```bash
k6 run tcp-latency-test.js
```

## Configuration

### Environment Variables

| Variable | Default | Description |
|----------|---------|-------------|
| `BASE_URL` | `http://localhost:3000` | BFF service URL |
| `PROMOTION_SERVICE_URL` | `http://localhost:3001` | Promotion service URL |
| `SCENARIO` | `smoke` | Test scenario to run |

### Test Scenarios

| Scenario | VUs | Duration | Purpose |
|----------|-----|----------|---------|
| `smoke` | 1 | 30s | Basic functionality verification |
| `load` | 10-20 | 16m | Normal expected load |
| `stress` | 20-100 | 23m | Beyond normal capacity |
| `spike` | 10-100 | 5m | Sudden load increases |
| `volume` | 50 | 10m | Large data volume |
| `soak` | 20 | 1h | Extended duration |

## Prerequisites

1. **Install k6**:
   ```bash
   # macOS
   brew install k6
   
   # Ubuntu/Debian
   sudo apt-key adv --keyserver hkp://keyserver.ubuntu.com:80 --recv-keys C5AD17C747E3415A3642D57D77C6C491D6AC1D69
   echo "deb https://dl.k6.io/deb stable main" | sudo tee /etc/apt/sources.list.d/k6.list
   sudo apt-get update
   sudo apt-get install k6
   
   # Windows
   choco install k6
   ```

2. **Start the services**:
   ```bash
   # Terminal 1: Start BFF service
   npm run start:dev
   
   # Terminal 2: Start Promotion service
   npm run start:promotion:dev
   ```

3. **Verify services are running**:
   ```bash
   curl http://localhost:3000/
   curl -X POST http://localhost:3000/proxy/promotion/health-check \
     -H "Content-Type: application/json" \
     -d '{"timeout": 0}'
   ```

## Running Tests

### Quick Start
```bash
# Run basic TCP transport test
npm run k6:tcp-test

# Run all test types
npm run k6:tcp-test && \
k6 run k6-scripts/tcp-connection-pool-test.js && \
k6 run k6-scripts/tcp-latency-test.js
```

### Advanced Usage
```bash
# Custom configuration
k6 run tcp-transport-test.js \
  --env BASE_URL=http://localhost:3000 \
  --env SCENARIO=load \
  --out json=results.json

# With specific VU count
k6 run tcp-transport-test.js --vus 50 --duration 5m

# Debug mode
k6 run tcp-transport-test.js --http-debug
```

## Metrics and Thresholds

### Key Metrics
- `tcp_response_time`: TCP transport response time
- `tcp_connection_time`: TCP connection establishment time
- `tcp_connection_reuse_rate`: Connection reuse efficiency
- `tcp_latency`: End-to-end latency measurements
- `http_req_duration`: HTTP request duration
- `http_req_failed`: HTTP request failure rate

### Performance Thresholds
- 95% of requests < 500ms response time
- 99% of requests < 1000ms response time
- Error rate < 10%
- TCP connection reuse rate > 80%
- Connection establishment time < 100ms

## Output and Reports

Tests generate multiple output formats:
- **Console**: Real-time progress and summary
- **HTML Report**: `tcp-transport-test-report.html`
- **Text Summary**: `tcp-transport-test-summary.txt`
- **JSON**: Raw metrics data (with `--out json=file.json`)

## Troubleshooting

### Common Issues

1. **Connection Refused**:
   ```
   Error: dial tcp 127.0.0.1:3000: connect: connection refused
   ```
   - Ensure BFF service is running on port 3000
   - Check if promotion service is running on port 3001

2. **High Error Rates**:
   - Check service logs for errors
   - Verify TCP transport configuration
   - Monitor system resources (CPU, memory)

3. **Slow Response Times**:
   - Check network latency
   - Monitor service performance
   - Verify database/external service performance

### Debug Commands
```bash
# Check service health
curl http://localhost:3000/
curl -X POST http://localhost:3000/proxy/promotion/health-check \
  -H "Content-Type: application/json" -d '{"timeout": 0}'

# Monitor service logs
npm run start:dev  # BFF logs
npm run start:promotion:dev  # Promotion service logs

# Run k6 with debug output
k6 run tcp-transport-test.js --http-debug --verbose
```

## Best Practices

1. **Baseline Testing**: Always run smoke tests first
2. **Gradual Load Increase**: Start with low load and gradually increase
3. **Monitor Resources**: Watch CPU, memory, and network during tests
4. **Consistent Environment**: Use the same test environment for comparisons
5. **Multiple Runs**: Run tests multiple times for statistical significance

## Contributing

When adding new test scenarios:
1. Follow the existing code structure
2. Add appropriate metrics and thresholds
3. Update this README with new scenarios
4. Include proper error handling and logging
