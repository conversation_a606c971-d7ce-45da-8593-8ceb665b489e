#!/bin/bash

# k6-scripts/run-tests.sh
# Test runner script for TCP transport performance tests

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
BASE_URL=${BASE_URL:-"http://localhost:3000"}
PROMOTION_SERVICE_URL=${PROMOTION_SERVICE_URL:-"http://localhost:3001"}
OUTPUT_DIR=${OUTPUT_DIR:-"./test-results"}
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")

# Create output directory
mkdir -p "$OUTPUT_DIR"

echo -e "${BLUE}🚀 TCP Transport Performance Test Suite${NC}"
echo -e "${BLUE}=======================================${NC}"
echo ""
echo -e "📊 Configuration:"
echo -e "   Base URL: ${BASE_URL}"
echo -e "   Promotion Service: ${PROMOTION_SERVICE_URL}"
echo -e "   Output Directory: ${OUTPUT_DIR}"
echo -e "   Timestamp: ${TIMESTAMP}"
echo ""

# Function to check if services are running
check_services() {
    echo -e "${YELLOW}🔍 Checking services...${NC}"
    
    # Check BFF service
    if curl -s -f "${BASE_URL}/" > /dev/null; then
        echo -e "${GREEN}✅ BFF service is running${NC}"
    else
        echo -e "${RED}❌ BFF service is not accessible at ${BASE_URL}${NC}"
        echo -e "${YELLOW}💡 Start it with: npm run start:dev${NC}"
        exit 1
    fi
    
    # Check TCP transport endpoint
    if curl -s -f -X POST "${BASE_URL}/proxy/promotion/health-check" \
        -H "Content-Type: application/json" \
        -d '{"timeout": 0}' > /dev/null; then
        echo -e "${GREEN}✅ TCP transport is working${NC}"
    else
        echo -e "${RED}❌ TCP transport is not working${NC}"
        echo -e "${YELLOW}💡 Make sure promotion service is running: npm run start:promotion:dev${NC}"
        exit 1
    fi
    
    echo ""
}

# Function to run a test with proper output handling
run_test() {
    local test_name="$1"
    local test_file="$2"
    local test_args="$3"
    local output_prefix="${OUTPUT_DIR}/${TIMESTAMP}_${test_name}"
    
    echo -e "${BLUE}🧪 Running ${test_name}...${NC}"
    echo -e "   Test file: ${test_file}"
    echo -e "   Arguments: ${test_args}"
    echo ""
    
    # Run the test with output capture
    if k6 run "${test_file}" ${test_args} \
        --out "json=${output_prefix}.json" \
        --summary-export="${output_prefix}_summary.json" \
        > "${output_prefix}.log" 2>&1; then
        
        echo -e "${GREEN}✅ ${test_name} completed successfully${NC}"
        
        # Extract key metrics from the log
        if grep -q "checks" "${output_prefix}.log"; then
            echo -e "${BLUE}📈 Key Results:${NC}"
            grep -E "(checks|http_req_duration|http_req_failed)" "${output_prefix}.log" | head -5
        fi
    else
        echo -e "${RED}❌ ${test_name} failed${NC}"
        echo -e "${YELLOW}📋 Last 10 lines of output:${NC}"
        tail -10 "${output_prefix}.log"
        return 1
    fi
    
    echo ""
}

# Function to generate summary report
generate_summary() {
    local summary_file="${OUTPUT_DIR}/${TIMESTAMP}_test_summary.md"
    
    echo -e "${BLUE}📊 Generating test summary...${NC}"
    
    cat > "$summary_file" << EOF
# TCP Transport Performance Test Summary

**Test Run**: ${TIMESTAMP}
**Base URL**: ${BASE_URL}
**Promotion Service**: ${PROMOTION_SERVICE_URL}

## Test Results

EOF

    # Add results for each test
    for result_file in "${OUTPUT_DIR}/${TIMESTAMP}"_*.json; do
        if [[ -f "$result_file" ]]; then
            test_name=$(basename "$result_file" .json | sed "s/${TIMESTAMP}_//")
            echo "### ${test_name}" >> "$summary_file"
            echo "" >> "$summary_file"
            
            # Extract key metrics if available
            if command -v jq > /dev/null; then
                echo "**Key Metrics:**" >> "$summary_file"
                jq -r '.metrics | to_entries[] | select(.key | test("http_req_duration|http_req_failed|checks")) | "- \(.key): \(.value.avg // .value.rate // .value.passes)/\(.value.count // .value.fails)"' "$result_file" >> "$summary_file" 2>/dev/null || echo "- Metrics extraction failed" >> "$summary_file"
            else
                echo "- Install jq for detailed metrics extraction" >> "$summary_file"
            fi
            
            echo "" >> "$summary_file"
        fi
    done
    
    echo -e "${GREEN}📄 Summary report generated: ${summary_file}${NC}"
}

# Main execution
main() {
    local test_type="${1:-all}"
    
    # Check services first
    check_services
    
    case "$test_type" in
        "smoke")
            run_test "smoke_test" "tcp-transport-test.js" "--env SCENARIO=smoke"
            ;;
        "load")
            run_test "load_test" "tcp-transport-test.js" "--env SCENARIO=load"
            ;;
        "stress")
            run_test "stress_test" "tcp-transport-test.js" "--env SCENARIO=stress"
            ;;
        "spike")
            run_test "spike_test" "tcp-transport-test.js" "--env SCENARIO=spike"
            ;;
        "connection-pool")
            run_test "connection_pool_test" "tcp-connection-pool-test.js" ""
            ;;
        "latency")
            run_test "latency_test" "tcp-latency-test.js" ""
            ;;
        "all")
            echo -e "${YELLOW}🔄 Running complete test suite...${NC}"
            echo ""
            
            run_test "smoke_test" "tcp-transport-test.js" "--env SCENARIO=smoke"
            run_test "connection_pool_test" "tcp-connection-pool-test.js" ""
            run_test "latency_test" "tcp-latency-test.js" ""
            run_test "load_test" "tcp-transport-test.js" "--env SCENARIO=load"
            ;;
        "help"|"-h"|"--help")
            echo "Usage: $0 [test_type]"
            echo ""
            echo "Available test types:"
            echo "  smoke         - Quick smoke test"
            echo "  load          - Load testing"
            echo "  stress        - Stress testing"
            echo "  spike         - Spike testing"
            echo "  connection-pool - Connection pool testing"
            echo "  latency       - Latency analysis"
            echo "  all           - Run all tests (default)"
            echo "  help          - Show this help"
            echo ""
            echo "Environment variables:"
            echo "  BASE_URL      - BFF service URL (default: http://localhost:3000)"
            echo "  OUTPUT_DIR    - Output directory (default: ./test-results)"
            exit 0
            ;;
        *)
            echo -e "${RED}❌ Unknown test type: $test_type${NC}"
            echo -e "${YELLOW}💡 Use '$0 help' for available options${NC}"
            exit 1
            ;;
    esac
    
    # Generate summary report
    generate_summary
    
    echo -e "${GREEN}🎉 Test execution completed!${NC}"
    echo -e "${BLUE}📁 Results saved in: ${OUTPUT_DIR}${NC}"
}

# Change to script directory
cd "$(dirname "$0")"

# Run main function with all arguments
main "$@"
