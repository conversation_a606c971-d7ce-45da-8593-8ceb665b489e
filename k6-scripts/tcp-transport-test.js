// k6-scripts/tcp-transport-test.js
// Main k6 script for testing TCP transport performance in NestJS microservices

import http from 'k6/http';
import { check, group, sleep } from 'k6';
import { htmlReport } from 'https://raw.githubusercontent.com/benc-uk/k6-reporter/main/dist/bundle.js';
import { textSummary } from 'https://jslib.k6.io/k6-summary/0.0.1/index.js';

// Import local modules
import { getScenarioConfig, getThresholds, getBaseConfig, getTestData } from './config.js';
import {
  validateTcpResponse,
  generateTestPayload,
  getRandomTimeout,
  randomSleep,
  logProgress,
  tcpResponseTime,
  tcpConnectionTime,
  tcpErrors,
  tcpSuccessRate,
} from './utils.js';

// Test configuration
const scenarioConfig = getScenarioConfig();
const baseConfig = getBaseConfig();
const testData = getTestData();

// k6 options
export const options = {
  scenarios: {
    tcp_transport_test: scenarioConfig,
  },
  thresholds: getThresholds(),
  
  // Additional options
  userAgent: 'k6-tcp-transport-test/1.0.0',
  timeout: '30s',
  
  // Tags for all requests
  tags: {
    test_name: 'tcp_transport_performance',
    service: 'nestjs_microservices',
  },
};

// Setup function - runs once before all VUs
export function setup() {
  console.log('🚀 Starting TCP Transport Performance Test');
  console.log(`📊 Scenario: ${__ENV.SCENARIO || 'smoke'}`);
  console.log(`🎯 Target URL: ${baseConfig.baseUrl}`);
  console.log(`⚙️  Configuration:`, JSON.stringify(scenarioConfig, null, 2));
  
  // Health check before starting the test
  const healthCheck = http.get(`${baseConfig.baseUrl}/`);
  if (healthCheck.status !== 200) {
    throw new Error(`Health check failed. BFF service not available at ${baseConfig.baseUrl}`);
  }
  
  console.log('✅ BFF service is healthy');
  
  return {
    startTime: Date.now(),
    baseUrl: baseConfig.baseUrl,
    testData: testData,
  };
}

// Main test function - runs for each VU iteration
export default function (data) {
  const baseUrl = data.baseUrl;
  
  group('TCP Transport Performance Tests', () => {
    
    // Test 1: Basic TCP communication with minimal timeout
    group('Basic TCP Communication', () => {
      const payload = { timeout: 0 };
      const startTime = Date.now();
      
      const response = http.post(
        `${baseUrl}/proxy/promotion/health-check`,
        JSON.stringify(payload),
        {
          headers: {
            'Content-Type': 'application/json',
            'X-Test-Type': 'basic-tcp',
            'X-Request-ID': `basic_${Date.now()}_${__VU}_${__ITER}`,
          },
          tags: { test_type: 'basic_tcp' },
        }
      );
      
      const isValid = validateTcpResponse(response, payload);
      tcpConnectionTime.add(Date.now() - startTime);
      
      if (!isValid) {
        logProgress('Basic TCP test failed', {
          status: response.status,
          vu: __VU,
          iteration: __ITER,
        });
      }
    });
    
    // Test 2: TCP communication with variable timeouts
    group('Variable Timeout TCP Communication', () => {
      const timeout = getRandomTimeout(testData.timeouts);
      const payload = { timeout };
      const startTime = Date.now();
      
      const response = http.post(
        `${baseUrl}/proxy/promotion/health-check`,
        JSON.stringify(payload),
        {
          headers: {
            'Content-Type': 'application/json',
            'X-Test-Type': 'variable-timeout',
            'X-Request-ID': `timeout_${timeout}_${Date.now()}_${__VU}_${__ITER}`,
            'X-Expected-Delay': timeout.toString(),
          },
          tags: { 
            test_type: 'variable_timeout',
            timeout_category: timeout < 500 ? 'fast' : timeout < 1000 ? 'medium' : 'slow',
          },
        }
      );
      
      const isValid = validateTcpResponse(response, payload);
      const actualDuration = Date.now() - startTime;
      
      // Verify that the response time includes the expected timeout
      const timeoutCheck = check(response, {
        'TCP timeout - response time includes expected delay': () => 
          actualDuration >= timeout && actualDuration < (timeout + 2000),
      });
      
      if (!isValid || !timeoutCheck) {
        logProgress('Variable timeout TCP test failed', {
          expectedTimeout: timeout,
          actualDuration,
          status: response.status,
          vu: __VU,
          iteration: __ITER,
        });
      }
    });
    
    // Test 3: Concurrent TCP requests simulation
    group('Concurrent TCP Requests', () => {
      const concurrentRequests = Math.min(5, Math.ceil(Math.random() * 3) + 1);
      const promises = [];
      
      for (let i = 0; i < concurrentRequests; i++) {
        const payload = { timeout: getRandomTimeout([0, 100, 200]) };
        const startTime = Date.now();
        
        const response = http.post(
          `${baseUrl}/proxy/promotion/health-check`,
          JSON.stringify(payload),
          {
            headers: {
              'Content-Type': 'application/json',
              'X-Test-Type': 'concurrent',
              'X-Request-ID': `concurrent_${i}_${Date.now()}_${__VU}_${__ITER}`,
              'X-Concurrent-Index': i.toString(),
            },
            tags: { 
              test_type: 'concurrent',
              concurrent_index: i.toString(),
            },
          }
        );
        
        validateTcpResponse(response, payload);
      }
      
      logProgress('Concurrent requests completed', {
        count: concurrentRequests,
        vu: __VU,
        iteration: __ITER,
      });
    });
    
    // Test 4: Error handling and resilience
    group('Error Handling and Resilience', () => {
      // Test with very high timeout to simulate slow responses
      const payload = { timeout: 5000 };
      const startTime = Date.now();
      
      const response = http.post(
        `${baseUrl}/proxy/promotion/health-check`,
        JSON.stringify(payload),
        {
          headers: {
            'Content-Type': 'application/json',
            'X-Test-Type': 'resilience',
            'X-Request-ID': `resilience_${Date.now()}_${__VU}_${__ITER}`,
          },
          tags: { test_type: 'resilience' },
          timeout: '10s', // Allow longer timeout for this test
        }
      );
      
      // For resilience testing, we expect the service to handle the request
      // even if it takes longer
      const resilienceCheck = check(response, {
        'Resilience - handles slow TCP responses': (r) => r.status === 200 || r.status === 408,
        'Resilience - response within reasonable time': (r) => r.timings.duration < 10000,
      });
      
      if (!resilienceCheck) {
        logProgress('Resilience test failed', {
          status: response.status,
          duration: response.timings.duration,
          vu: __VU,
          iteration: __ITER,
        });
      }
    });
  });
  
  // Add some randomness to simulate real user behavior
  randomSleep(0.5, 2);
}

// Teardown function - runs once after all VUs complete
export function teardown(data) {
  const endTime = Date.now();
  const totalDuration = endTime - data.startTime;
  
  console.log('🏁 TCP Transport Performance Test Completed');
  console.log(`⏱️  Total test duration: ${(totalDuration / 1000).toFixed(2)} seconds`);
  console.log('📈 Check the detailed report for performance metrics');
}

// Custom summary report
export function handleSummary(data) {
  return {
    'tcp-transport-test-report.html': htmlReport(data),
    'tcp-transport-test-summary.txt': textSummary(data, { indent: ' ', enableColors: true }),
    stdout: textSummary(data, { indent: ' ', enableColors: true }),
  };
}
