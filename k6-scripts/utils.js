// k6-scripts/utils.js
// Utility functions for k6 TCP transport testing

import { check, sleep } from 'k6';
import { Rate, Trend, Counter } from 'k6/metrics';

// Custom metrics for TCP transport performance
export const tcpResponseTime = new Trend('tcp_response_time');
export const tcpConnectionTime = new Trend('tcp_connection_time');
export const tcpErrors = new Counter('tcp_errors');
export const tcpSuccessRate = new Rate('tcp_success_rate');

/**
 * Generate random timeout value for testing
 * @param {Array} timeouts - Array of possible timeout values
 * @returns {number} Random timeout value
 */
export function getRandomTimeout(timeouts = [0, 100, 500, 1000]) {
  return timeouts[Math.floor(Math.random() * timeouts.length)];
}

/**
 * Generate test payload based on size
 * @param {string} size - Size category: 'small', 'medium', 'large'
 * @returns {Object} Test payload
 */
export function generateTestPayload(size = 'small') {
  const basePayload = {
    timeout: getRandomTimeout(),
    timestamp: Date.now(),
    requestId: generateRequestId(),
  };

  switch (size) {
    case 'small':
      return basePayload;
    
    case 'medium':
      return {
        ...basePayload,
        data: generateRandomString(1000), // 1KB of data
        metadata: {
          source: 'k6-test',
          version: '1.0.0',
          environment: 'test',
        },
      };
    
    case 'large':
      return {
        ...basePayload,
        data: generateRandomString(10000), // 10KB of data
        metadata: {
          source: 'k6-test',
          version: '1.0.0',
          environment: 'test',
          description: generateRandomString(500),
          tags: Array.from({ length: 10 }, () => generateRandomString(50)),
        },
        additionalData: Array.from({ length: 100 }, (_, i) => ({
          id: i,
          value: generateRandomString(100),
        })),
      };
    
    default:
      return basePayload;
  }
}

/**
 * Generate random string of specified length
 * @param {number} length - Length of the string
 * @returns {string} Random string
 */
export function generateRandomString(length) {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  let result = '';
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
}

/**
 * Generate unique request ID
 * @returns {string} Unique request ID
 */
export function generateRequestId() {
  return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

/**
 * Validate TCP response
 * @param {Object} response - HTTP response object
 * @param {Object} payload - Original request payload
 * @returns {boolean} True if response is valid
 */
export function validateTcpResponse(response, payload) {
  const checks = check(response, {
    'TCP transport - status is 200': (r) => r.status === 200,
    'TCP transport - response time < 5s': (r) => r.timings.duration < 5000,
    'TCP transport - has response body': (r) => r.body && r.body.length > 0,
    'TCP transport - response is JSON': (r) => {
      try {
        JSON.parse(r.body);
        return true;
      } catch (e) {
        return false;
      }
    },
  });

  // Additional validation for response content
  if (response.status === 200) {
    try {
      const responseData = JSON.parse(response.body);
      const contentChecks = check(responseData, {
        'TCP response - has success field': (data) => data.hasOwnProperty('success'),
        'TCP response - success is true': (data) => data.success === true,
        'TCP response - has data field': (data) => data.hasOwnProperty('data'),
        'TCP response - data is array': (data) => Array.isArray(data.data),
      });
      
      // Record custom metrics
      tcpResponseTime.add(response.timings.duration);
      tcpSuccessRate.add(1);
      
      return checks && contentChecks;
    } catch (e) {
      tcpErrors.add(1);
      tcpSuccessRate.add(0);
      return false;
    }
  } else {
    tcpErrors.add(1);
    tcpSuccessRate.add(0);
    return false;
  }
}

/**
 * Add random delay between requests
 * @param {number} min - Minimum delay in seconds
 * @param {number} max - Maximum delay in seconds
 */
export function randomSleep(min = 1, max = 3) {
  const delay = Math.random() * (max - min) + min;
  sleep(delay);
}

/**
 * Log test progress
 * @param {string} message - Log message
 * @param {Object} data - Additional data to log
 */
export function logProgress(message, data = {}) {
  console.log(`[${new Date().toISOString()}] ${message}`, JSON.stringify(data));
}

/**
 * Calculate percentile from array of values
 * @param {Array} values - Array of numeric values
 * @param {number} percentile - Percentile to calculate (0-100)
 * @returns {number} Percentile value
 */
export function calculatePercentile(values, percentile) {
  const sorted = values.slice().sort((a, b) => a - b);
  const index = Math.ceil((percentile / 100) * sorted.length) - 1;
  return sorted[index];
}

/**
 * Format duration in milliseconds to human readable format
 * @param {number} ms - Duration in milliseconds
 * @returns {string} Formatted duration
 */
export function formatDuration(ms) {
  if (ms < 1000) return `${ms.toFixed(2)}ms`;
  if (ms < 60000) return `${(ms / 1000).toFixed(2)}s`;
  return `${(ms / 60000).toFixed(2)}m`;
}
