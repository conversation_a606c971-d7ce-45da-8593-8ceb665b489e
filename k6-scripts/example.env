# k6-scripts/example.env
# Example environment configuration for k6 TCP transport tests
# Copy this file to .env and modify as needed

# Service URLs
BASE_URL=http://localhost:3000
PROMOTION_SERVICE_URL=http://localhost:3001

# Test Configuration
SCENARIO=smoke
VUS=10
DURATION=5m

# Output Configuration
OUTPUT_DIR=./test-results
ENABLE_HTML_REPORT=true
ENABLE_JSON_OUTPUT=true

# Performance Thresholds (optional overrides)
MAX_RESPONSE_TIME=500
MAX_ERROR_RATE=0.1
MIN_SUCCESS_RATE=0.9

# Debug Options
HTTP_DEBUG=false
VERBOSE=false

# Load Test Specific
LOAD_TEST_STAGES="2m:10,5m:10,2m:20,5m:20,2m:0"

# Stress Test Specific  
STRESS_TEST_MAX_VUS=100
STRESS_TEST_DURATION=20m

# Connection Pool Test Specific
CONNECTION_POOL_MAX_CONNECTIONS=50
CONNECTION_REUSE_THRESHOLD=0.8

# Latency Test Specific
LATENCY_BASELINE_DURATION=2m
LATENCY_P95_THRESHOLD=300
LATENCY_P99_THRESHOLD=500
