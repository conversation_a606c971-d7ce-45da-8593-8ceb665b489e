// k6-scripts/config.js
// Configuration for different test scenarios

export const config = {
  // Base configuration
  base: {
    baseUrl: __ENV.BASE_URL || 'http://localhost:3000',
    promotionServiceUrl: __ENV.PROMOTION_SERVICE_URL || 'http://localhost:3001',
  },

  // Test scenarios
  scenarios: {
    // Smoke test - minimal load to verify functionality
    smoke: {
      executor: 'constant-vus',
      vus: 1,
      duration: '30s',
      tags: { test_type: 'smoke' },
    },

    // Load test - normal expected load
    load: {
      executor: 'ramping-vus',
      startVUs: 0,
      stages: [
        { duration: '2m', target: 10 }, // Ramp up to 10 users
        { duration: '5m', target: 10 }, // Stay at 10 users
        { duration: '2m', target: 20 }, // Ramp up to 20 users
        { duration: '5m', target: 20 }, // Stay at 20 users
        { duration: '2m', target: 0 },  // Ramp down
      ],
      tags: { test_type: 'load' },
    },

    // Stress test - beyond normal capacity
    stress: {
      executor: 'ramping-vus',
      startVUs: 0,
      stages: [
        { duration: '2m', target: 20 }, // Ramp up to 20 users
        { duration: '5m', target: 20 }, // Stay at 20 users
        { duration: '2m', target: 50 }, // Ramp up to 50 users
        { duration: '5m', target: 50 }, // Stay at 50 users
        { duration: '2m', target: 100 }, // Ramp up to 100 users
        { duration: '5m', target: 100 }, // Stay at 100 users
        { duration: '2m', target: 0 },   // Ramp down
      ],
      tags: { test_type: 'stress' },
    },

    // Spike test - sudden load increase
    spike: {
      executor: 'ramping-vus',
      startVUs: 0,
      stages: [
        { duration: '1m', target: 10 },  // Normal load
        { duration: '30s', target: 100 }, // Spike to 100 users
        { duration: '2m', target: 100 },  // Stay at spike
        { duration: '30s', target: 10 },  // Drop back to normal
        { duration: '1m', target: 0 },    // Ramp down
      ],
      tags: { test_type: 'spike' },
    },

    // Volume test - large amount of data
    volume: {
      executor: 'constant-vus',
      vus: 50,
      duration: '10m',
      tags: { test_type: 'volume' },
    },

    // Soak test - extended duration
    soak: {
      executor: 'constant-vus',
      vus: 20,
      duration: '1h',
      tags: { test_type: 'soak' },
    },
  },

  // Thresholds for performance criteria
  thresholds: {
    // HTTP request duration thresholds
    http_req_duration: [
      'p(95)<500',   // 95% of requests should be below 500ms
      'p(99)<1000',  // 99% of requests should be below 1s
    ],
    
    // HTTP request failed rate
    http_req_failed: ['rate<0.1'], // Error rate should be less than 10%
    
    // Checks success rate
    checks: ['rate>0.9'], // 90% of checks should pass
    
    // Custom TCP transport metrics
    tcp_response_time: ['p(95)<300'], // TCP response time should be under 300ms
    tcp_connection_time: ['p(95)<100'], // TCP connection time should be under 100ms
  },

  // Test data configurations
  testData: {
    timeouts: [0, 100, 500, 1000, 2000], // Different timeout values to test
    payloadSizes: ['small', 'medium', 'large'], // Different payload sizes
  },
};

// Get scenario configuration based on environment variable
export function getScenarioConfig() {
  const scenario = __ENV.SCENARIO || 'smoke';
  return config.scenarios[scenario] || config.scenarios.smoke;
}

// Get thresholds configuration
export function getThresholds() {
  return config.thresholds;
}

// Get base configuration
export function getBaseConfig() {
  return config.base;
}

// Get test data configuration
export function getTestData() {
  return config.testData;
}
