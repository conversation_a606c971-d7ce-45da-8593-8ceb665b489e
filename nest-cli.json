{"$schema": "https://json.schemastore.org/nest-cli", "collection": "@nestjs/schematics", "sourceRoot": "apps/bff/src", "compilerOptions": {"deleteOutDir": true, "webpack": true, "tsConfigPath": "apps/bff/tsconfig.app.json"}, "monorepo": true, "root": "apps/bff", "projects": {"bff": {"type": "application", "root": "apps/bff", "entryFile": "main", "sourceRoot": "apps/bff/src", "compilerOptions": {"tsConfigPath": "apps/bff/tsconfig.app.json"}}, "promotion": {"type": "application", "root": "apps/promotion", "entryFile": "main", "sourceRoot": "apps/promotion/src", "compilerOptions": {"tsConfigPath": "apps/promotion/tsconfig.app.json"}}}}