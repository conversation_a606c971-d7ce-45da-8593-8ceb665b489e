import { Controller, Get } from '@nestjs/common';
import { PromotionService } from './promotion.service';
import { MessagePattern, Payload } from '@nestjs/microservices';

const responseData = {
  success: true,
  data: [
    {
      id: 5,
      name: '<PERSON><PERSON><PERSON>',
      code: 'dongxoai',
      latitude: 11.53389619,
      longitude: 106.8874348,
      max_radius_in_meters: 25000,
      parent_id: null,
      is_active: 1,
      created_at: '2024-03-11T13:55:30.000Z',
      updated_at: '2021-11-26T15:56:06.000Z',
      merchantHasRoles: [
        {
          merchant_id: 24607,
          role_id: 1,
          restaurant_id: 3266,
          province_id: 5,
          global_restaurant_id: 23551,
          is_active: 1,
          restaurant: {
            id: 3266,
            name: '<PERSON><PERSON> - <PERSON><PERSON><PERSON>',
            image:
              'https://cdn.villship.com/file/restaurants/3266988c12d1-3585-4ed2-87aa-db46e958d4b7.jpg',
            background: null,
            address:
              '<PERSON><PERSON><PERSON><PERSON>, Ph<PERSON><PERSON>ng Tân Đồng, T<PERSON> Đồng Xoà<PERSON>, Tỉnh Bình Phước',
            latitude: '11.538975319356354',
            longitude: '106.90902869469602',
            phone: '0352171597',
            mobile: '0352171597',
            cooperating: true,
            province_id: 5,
            sub_province_id: 5,
            restaurant_id: 3266,
            seller_id: 926520,
            author_id: null,
            is_trial: false,
            trade_discount_period_type: 'direct',
            trade_discount_type: 0,
            trade_discount: 10,
            operating_status: 'open',
            reopen_time: null,
            code: 'MDX-02050',
            thumbnails:
              'https://cdn.villship.com/thumbnail/file/restaurants/3266988c12d1-3585-4ed2-87aa-db46e958d4b7.jpg',
            created_at: '2023-05-06T03:25:08.000Z',
            updated_at: '2025-04-14T07:25:51.000Z',
            deleted_at: null,
          },
        },
        {
          merchant_id: 24607,
          role_id: 1,
          restaurant_id: 3566,
          province_id: 5,
          global_restaurant_id: 23850,
          is_active: 1,
          restaurant: {
            id: 3566,
            name: 'Mixue Cơ Sở 2 - Tôn Đức Thắng',
            image:
              'https://cdn.villship.com/file/restaurants/3566760d5b1d-f3ba-49a1-8db7-9b4d51d2fb2e.jpg',
            background: null,
            address: '849 Tôn Đức Thắng, Tiến Thành, TP Đồng Xoài, Bình Phước',
            latitude: '11.523949744185002',
            longitude: '106.86784200297319',
            phone: '0988900420',
            mobile: '0988900420',
            cooperating: true,
            province_id: 5,
            sub_province_id: 5,
            restaurant_id: 3566,
            seller_id: 926520,
            author_id: null,
            is_trial: false,
            trade_discount_period_type: 'direct',
            trade_discount_type: 0,
            trade_discount: 10,
            operating_status: 'open',
            reopen_time: null,
            code: 'MDX-02349',
            thumbnails:
              'https://cdn.villship.com/thumbnail/file/restaurants/3566760d5b1d-f3ba-49a1-8db7-9b4d51d2fb2e.jpg',
            created_at: '2023-07-28T01:31:12.000Z',
            updated_at: '2025-05-04T17:00:31.000Z',
            deleted_at: null,
          },
        },
      ],
      restaurants: [
        {
          id: 3266,
          name: 'Kem - Trà Sữa Mixue - Nguyễn Huệ',
          image:
            'https://cdn.villship.com/file/restaurants/3266988c12d1-3585-4ed2-87aa-db46e958d4b7.jpg',
          background: null,
          address: 'Nguyễn Huệ, Phường Tân Đồng, TP Đồng Xoài, Tỉnh Bình Phước',
          latitude: '11.538975319356354',
          longitude: '106.90902869469602',
          phone: '0352171597',
          mobile: '0352171597',
          cooperating: true,
          province_id: 5,
          sub_province_id: 5,
          restaurant_id: 3266,
          seller_id: 926520,
          author_id: null,
          is_trial: false,
          trade_discount_period_type: 'direct',
          trade_discount_type: 0,
          trade_discount: 10,
          operating_status: 'open',
          reopen_time: null,
          code: 'MDX-02050',
          thumbnails:
            'https://cdn.villship.com/thumbnail/file/restaurants/3266988c12d1-3585-4ed2-87aa-db46e958d4b7.jpg',
          created_at: '2023-05-06T03:25:08.000Z',
          updated_at: '2025-04-14T07:25:51.000Z',
          deleted_at: null,
        },
        {
          id: 3566,
          name: 'Mixue Cơ Sở 2 - Tôn Đức Thắng',
          image:
            'https://cdn.villship.com/file/restaurants/3566760d5b1d-f3ba-49a1-8db7-9b4d51d2fb2e.jpg',
          background: null,
          address: '849 Tôn Đức Thắng, Tiến Thành, TP Đồng Xoài, Bình Phước',
          latitude: '11.523949744185002',
          longitude: '106.86784200297319',
          phone: '0988900420',
          mobile: '0988900420',
          cooperating: true,
          province_id: 5,
          sub_province_id: 5,
          restaurant_id: 3566,
          seller_id: 926520,
          author_id: null,
          is_trial: false,
          trade_discount_period_type: 'direct',
          trade_discount_type: 0,
          trade_discount: 10,
          operating_status: 'open',
          reopen_time: null,
          code: 'MDX-02349',
          thumbnails:
            'https://cdn.villship.com/thumbnail/file/restaurants/3566760d5b1d-f3ba-49a1-8db7-9b4d51d2fb2e.jpg',
          created_at: '2023-07-28T01:31:12.000Z',
          updated_at: '2025-05-04T17:00:31.000Z',
          deleted_at: null,
        },
      ],
    },
  ],
  message: '',
};

@Controller()
export class PromotionController {
  constructor(private readonly promotionService: PromotionService) {}

  @MessagePattern({ cmd: 'health-check' })
  getHello(@Payload() payload: { timeout: number }): Promise<any> {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve(responseData);
      }, payload.timeout || 0);
    });
    // return this.promotionService.getHello();
  }
}
