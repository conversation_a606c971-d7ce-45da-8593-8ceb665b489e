import { Body, Controller, Inject, ParseIntPipe, Post } from '@nestjs/common';
import { ClientProxy } from '@nestjs/microservices';

@Controller('proxy/promotion')
export class ProxyController {
  constructor(
    @Inject('PROMOTION_SERVICE') private readonly client: ClientProxy,
  ) {}

  @Post('health-check')
  async healthCheck(@Body('timeout', new ParseIntPipe()) timeout: number) {
    return this.client.send(
      { cmd: 'health-check' },
      {
        timeout,
      },
    );
  }
}
